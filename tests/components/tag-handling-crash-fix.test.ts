import { mount, unmount } from 'svelte';
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import PropertyDisplay from '../../src/components/PropertyDisplay.svelte';
import { createMockContainer, cleanupContainer } from '../test-utils';

describe('Tag Handling Crash Fix', () => {
  let container: HTMLElement;
  let component: any;

  beforeEach(() => {
    container = createMockContainer();
  });

  afterEach(() => {
    if (component) {
      unmount(component);
      component = null;
    }
    cleanupContainer(container);
  });

  it('should handle null tags without crashing', () => {
    const props = {
      label: 'Tags',
      value: [null, undefined, { name: 'valid-tag' }, null],
      isTagsList: true,
      status: 'synced' as const
    };

    expect(() => {
      component = mount(PropertyDisplay, { target: container, props });
    }).not.toThrow();
  });

  it('should handle empty tag names without crashing', () => {
    const props = {
      label: 'Tags',
      value: [
        { name: '' },
        { name: null },
        { name: undefined },
        { name: 'valid-tag' },
        ''
      ],
      isTagsList: true,
      status: 'synced' as const
    };

    expect(() => {
      component = mount(PropertyDisplay, { target: container, props });
    }).not.toThrow();
  });

  it('should handle mixed tag formats without crashing', () => {
    const props = {
      label: 'Tags',
      value: [
        'string-tag',
        { name: 'object-tag' },
        null,
        undefined,
        { name: '' },
        { name: null },
        42, // Invalid type
        { notName: 'invalid-object' }
      ],
      isTagsList: true,
      status: 'synced' as const
    };

    expect(() => {
      component = mount(PropertyDisplay, { target: container, props });
    }).not.toThrow();
  });

  it('should handle non-array tag values without crashing', () => {
    const props = {
      label: 'Tags',
      value: null as any,
      isTagsList: true,
      status: 'synced' as const
    };

    expect(() => {
      component = mount(PropertyDisplay, { target: container, props });
    }).not.toThrow();
  });

  it('should handle undefined tag values without crashing', () => {
    const props = {
      label: 'Tags',
      value: undefined as any,
      isTagsList: true,
      status: 'synced' as const
    };

    expect(() => {
      component = mount(PropertyDisplay, { target: container, props });
    }).not.toThrow();
  });

  it('should filter out invalid tags and display only valid ones', () => {
    const props = {
      label: 'Tags',
      value: [
        null,
        undefined,
        '',
        'valid-tag-1',
        { name: 'valid-tag-2' },
        { name: '' },
        { name: null },
        'valid-tag-3'
      ],
      isTagsList: true,
      status: 'synced' as const
    };

    component = mount(PropertyDisplay, { target: container, props });

    // Should render without crashing
    expect(container).toBeTruthy();

    // Should contain the valid tags
    const tagElements = container.querySelectorAll('.ghost-tag-pill');
    expect(tagElements.length).toBe(3); // Only the 3 valid tags
  });
});
