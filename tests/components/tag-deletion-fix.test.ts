import { mount, unmount } from 'svelte';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import GhostSyncView from '../../src/components/GhostSyncView.svelte';
import type { SyncStatusData } from '../../src/components/types';
import { createMockContainer, cleanupContainer } from '../test-utils';

describe('Tag Deletion Fix', () => {
  let container: HTMLElement;
  let mockPlugin: any;
  let component: any;

  beforeEach(() => {
    container = createMockContainer();

    mockPlugin = {
      settings: {
        articlesDir: 'articles'
      }
    };
  });

  afterEach(() => {
    if (component) {
      unmount(component);
      component = null;
    }
    cleanupContainer(container);
  });

  it('should display local tags when tags status is different', () => {
    const mockFile = {
      path: 'articles/test-post.md',
      name: 'test-post.md'
    };

    // Create sync status with different tags and local tags
    const syncStatus: SyncStatusData = {
      title: 'synced',
      slug: 'synced',
      status: 'synced',
      tags: 'different', // This is the key - tags are different
      featured: 'synced',
      feature_image: 'synced',
      visibility: 'synced',
      primary_tag: 'synced',
      created_at: 'synced',
      updated_at: 'synced',
      published_at: 'synced',
      synced_at: 'synced',
      newsletter: 'synced',
      email_sent: 'synced',
      ghostPost: {
        id: 'test-id',
        title: 'Test Post',
        slug: 'test-post',
        status: 'draft',
        featured: false,
        created_at: '2024-01-01T00:00:00.000Z',
        updated_at: '2024-01-01T00:00:00.000Z',
        tags: [
          { name: 'tag1', slug: 'tag1' },
          { name: 'tag2', slug: 'tag2' },
          { name: 'tag3', slug: 'tag3' }
        ]
      },
      localTags: ['tag1', 'tag2'] // Local has only 2 tags (tag3 was deleted)
    };

    component = mount(GhostSyncView, {
      target: container,
      props: {
        currentFile: mockFile,
        syncStatus,
        plugin: mockPlugin
      }
    });

    // Check that the tags section shows the local tags (tag1, tag2) not the Ghost tags (tag1, tag2, tag3)
    const tagsSection = container.querySelector('.ghost-property-section');
    expect(tagsSection).toBeTruthy();

    // Should show refresh icon because tags are different
    const refreshIcon = container.querySelector('.ghost-property-refresh-icon');
    expect(refreshIcon).toBeTruthy();
    expect(refreshIcon?.textContent).toBe('🔄');

    // Should display the local tags (2 tags) not the Ghost tags (3 tags)
    const tagPills = container.querySelectorAll('.ghost-tag-pill');
    expect(tagPills).toHaveLength(2); // Should show 2 local tags, not 3 Ghost tags

    // Verify the tag names are from local tags
    const tagNames = Array.from(tagPills).map(pill =>
      pill.querySelector('.ghost-tag-name')?.textContent
    );
    expect(tagNames).toEqual(['tag1', 'tag2']);
  });

  it('should display Ghost tags when tags status is synced', () => {
    const mockFile = {
      path: 'articles/test-post.md',
      name: 'test-post.md'
    };

    // Create sync status with synced tags
    const syncStatus: SyncStatusData = {
      title: 'synced',
      slug: 'synced',
      status: 'synced',
      tags: 'synced', // Tags are synced
      featured: 'synced',
      feature_image: 'synced',
      visibility: 'synced',
      primary_tag: 'synced',
      created_at: 'synced',
      updated_at: 'synced',
      published_at: 'synced',
      synced_at: 'synced',
      newsletter: 'synced',
      email_sent: 'synced',
      ghostPost: {
        id: 'test-id',
        title: 'Test Post',
        slug: 'test-post',
        status: 'draft',
        featured: false,
        created_at: '2024-01-01T00:00:00.000Z',
        updated_at: '2024-01-01T00:00:00.000Z',
        tags: [
          { name: 'tag1', slug: 'tag1' },
          { name: 'tag2', slug: 'tag2' },
          { name: 'tag3', slug: 'tag3' }
        ]
      },
      localTags: ['tag1', 'tag2', 'tag3'] // Local has same tags as Ghost
    };

    component = mount(GhostSyncView, {
      target: container,
      props: {
        currentFile: mockFile,
        syncStatus,
        plugin: mockPlugin
      }
    });

    // Should NOT show refresh icon because tags are synced
    const refreshIcon = container.querySelector('.ghost-property-refresh-icon');
    expect(refreshIcon).toBeFalsy();

    // Should display the Ghost tags (3 tags)
    const tagPills = container.querySelectorAll('.ghost-tag-pill');
    expect(tagPills).toHaveLength(3);

    // Verify the tag names are from Ghost tags
    const tagNames = Array.from(tagPills).map(pill =>
      pill.querySelector('.ghost-tag-name')?.textContent
    );
    expect(tagNames).toEqual(['tag1', 'tag2', 'tag3']);
  });

  it('should display Ghost tags when localTags is not available', () => {
    const mockFile = {
      path: 'articles/test-post.md',
      name: 'test-post.md'
    };

    // Create sync status with different tags but no localTags
    const syncStatus: SyncStatusData = {
      title: 'synced',
      slug: 'synced',
      status: 'synced',
      tags: 'different', // Tags are different but no localTags provided
      featured: 'synced',
      feature_image: 'synced',
      visibility: 'synced',
      primary_tag: 'synced',
      created_at: 'synced',
      updated_at: 'synced',
      published_at: 'synced',
      synced_at: 'synced',
      newsletter: 'synced',
      email_sent: 'synced',
      ghostPost: {
        id: 'test-id',
        title: 'Test Post',
        slug: 'test-post',
        status: 'draft',
        featured: false,
        created_at: '2024-01-01T00:00:00.000Z',
        updated_at: '2024-01-01T00:00:00.000Z',
        tags: [
          { name: 'tag1', slug: 'tag1' },
          { name: 'tag2', slug: 'tag2' },
          { name: 'tag3', slug: 'tag3' }
        ]
      }
      // No localTags property
    };

    component = mount(GhostSyncView, {
      target: container,
      props: {
        currentFile: mockFile,
        syncStatus,
        plugin: mockPlugin
      }
    });

    // Should show refresh icon because tags are different
    const refreshIcon = container.querySelector('.ghost-property-refresh-icon');
    expect(refreshIcon).toBeTruthy();

    // Should fall back to displaying Ghost tags when localTags is not available
    const tagPills = container.querySelectorAll('.ghost-tag-pill');
    expect(tagPills).toHaveLength(3);

    // Verify the tag names are from Ghost tags
    const tagNames = Array.from(tagPills).map(pill =>
      pill.querySelector('.ghost-tag-name')?.textContent
    );
    expect(tagNames).toEqual(['tag1', 'tag2', 'tag3']);
  });
});
