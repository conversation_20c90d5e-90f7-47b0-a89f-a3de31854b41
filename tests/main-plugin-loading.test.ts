import { describe, it, expect, vi, beforeEach } from 'vitest';
import GhostSyncPlugin from '../src/main';
import { App, Plugin } from 'obsidian';

// Mock Obsidian modules
vi.mock('obsidian', () => ({
  App: vi.fn(),
  Plugin: vi.fn(),
  Notice: vi.fn(),
  MarkdownView: vi.fn(),
  Modal: vi.fn(),
  SuggestModal: vi.fn(),
  TFile: vi.fn(),
  WorkspaceLeaf: vi.fn(),
}));

// Mock other dependencies
vi.mock('../src/utils/content-converter', () => ({
  ContentConverter: vi.fn(),
}));

vi.mock('../src/utils/property-mapping', () => ({
  PropertyMapper: vi.fn(),
}));

vi.mock('../src/api/ghost-api', () => ({
  ObsidianGhostAPI: vi.fn(),
}));

vi.mock('../src/views/sync-status-view', () => ({
  SvelteSyncStatusView: vi.fn(),
  VIEW_TYPE_GHOST_SYNC_STATUS: 'ghost-sync-status',
}));

vi.mock('../src/settings/settings-tab', () => ({
  GhostSyncSettingTab: vi.fn(),
}));

vi.mock('../src/services/sync-metadata-storage', () => ({
  SyncMetadataStorage: vi.fn().mockImplementation(() => ({
    load: vi.fn().mockResolvedValue(undefined),
  })),
}));

vi.mock('../src/services/smart-sync-service', () => ({
  SmartSyncService: vi.fn(),
}));

vi.mock('../src/services/obsidian-app-adapter', () => ({
  ObsidianAppAdapter: vi.fn(),
}));

vi.mock('../src/markdown', () => ({
  Markdown: vi.fn(),
}));

describe('GhostSyncPlugin Loading', () => {
  let plugin: GhostSyncPlugin;
  let mockApp: any;

  beforeEach(() => {
    mockApp = {
      workspace: {
        getUnpinnedLeaf: vi.fn(),
      },
      vault: {
        adapter: {
          exists: vi.fn(),
        },
      },
    };

    plugin = new GhostSyncPlugin(mockApp, {
      id: 'ghost-sync',
      name: 'Ghost Sync',
      version: '1.0.0',
      minAppVersion: '0.15.0',
      description: 'Test plugin',
      author: 'Test',
      authorUrl: '',
      isDesktopOnly: false,
    });

    // Mock plugin methods
    plugin.loadSettings = vi.fn().mockResolvedValue(undefined);
    plugin.addCommand = vi.fn();
    plugin.addSettingTab = vi.fn();
    plugin.registerView = vi.fn();
    plugin.loadNewsletters = vi.fn();

    // Set default settings
    plugin.settings = {
      ghostUrl: 'https://example.ghost.io',
      ghostAdminApiKey: 'test-key:test-secret',
      articlesDir: 'articles',
      verbose: false,
    };
  });

  it('should load successfully with valid settings', async () => {
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => { });

    await plugin.onload();

    expect(plugin.loadSettings).toHaveBeenCalled();
    expect(plugin.addCommand).toHaveBeenCalledTimes(6);
    expect(plugin.addSettingTab).toHaveBeenCalled();
    expect(plugin.registerView).toHaveBeenCalled();
    expect(consoleSpy).toHaveBeenCalledWith('Ghost Sync plugin loaded successfully');

    consoleSpy.mockRestore();
  });

  it('should handle loading errors gracefully', async () => {
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => { });
    const mockError = new Error('Test loading error');

    // Make loadSettings throw an error
    plugin.loadSettings = vi.fn().mockRejectedValue(mockError);

    await plugin.onload();

    expect(consoleErrorSpy).toHaveBeenCalledWith('=== GHOST SYNC PLUGIN LOADING ERROR ===');
    expect(consoleErrorSpy).toHaveBeenCalledWith('Error during plugin initialization:', mockError);
    expect(consoleErrorSpy).toHaveBeenCalledWith('Stack trace:', mockError.stack);
    expect(consoleErrorSpy).toHaveBeenCalledWith('=== END GHOST SYNC PLUGIN LOADING ERROR ===');

    // Should still try to register settings tab
    expect(plugin.addSettingTab).toHaveBeenCalled();

    consoleErrorSpy.mockRestore();
  });

  it('should load with invalid settings but show warning', async () => {
    const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => { });

    // Set invalid settings
    plugin.settings = {
      ghostUrl: '',
      ghostAdminApiKey: '',
      articlesDir: 'articles',
      verbose: false,
    };

    await plugin.onload();

    expect(consoleWarnSpy).toHaveBeenCalledWith('Ghost Sync: Ghost site URL not configured');
    expect(plugin.registerView).toHaveBeenCalled(); // Should still register view with null services

    consoleWarnSpy.mockRestore();
  });
});
