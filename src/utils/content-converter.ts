// Import HTML to Markdown converter
const TurndownService = require('turndown');
import { PropertyMapper } from './property-mapping';
import { markdownToLexical, lexicalToMarkdown } from '../markdown';

// Conditional import for getFrontMatterInfo to handle different environments
let getFrontMatterInfo: any;
try {
  // This will work in the actual Obsidian environment
  const obsidian = require('obsidian');
  getFrontMatterInfo = obsidian.getFrontMatterInfo;
} catch (error) {
  // Fallback for environments where obsidian module is not available
  getFrontMatterInfo = (content: string) => {
    if (content.startsWith('---\n')) {
      const frontmatterEnd = content.indexOf('\n---\n', 4);
      if (frontmatterEnd !== -1) {
        return {
          exists: true,
          from: 4,
          to: frontmatterEnd,
          contentStart: frontmatterEnd + 5,
          frontmatter: content.slice(4, frontmatterEnd)
        };
      }
    }
    return {
      exists: false,
      from: 0,
      to: 0,
      contentStart: 0,
      frontmatter: ''
    };
  };
}

// Content conversion utilities
export class ContentConverter {
  static htmlToMarkdown(html: string): string {
    if (!html) return '';

    // Use Turndown for proper HTML to Markdown conversion
    const turndownService = new TurndownService({
      headingStyle: 'atx',
      codeBlockStyle: 'fenced',
      fence: '```',
      emDelimiter: '*',
      strongDelimiter: '**',
      linkStyle: 'inlined',
      linkReferenceStyle: 'full'
    });

    // Add custom rule for code blocks with language detection
    turndownService.addRule('codeBlock', {
      filter: function (node: any) {
        return node.nodeName === 'PRE' && node.firstChild && node.firstChild.nodeName === 'CODE';
      },
      replacement: function (content: string, node: any) {
        const codeElement = node.firstChild;
        const className = codeElement.className || '';
        const languageMatch = className.match(/language-(\w+)/);
        const language = languageMatch ? languageMatch[1] : '';

        // Clean up the content by removing extra whitespace
        const cleanContent = content.trim();

        return '\n\n```' + language + '\n' + cleanContent + '\n```\n\n';
      }
    });

    // Add custom rule for Ghost callout cards to convert back to Obsidian callouts
    turndownService.addRule('calloutCard', {
      filter: function (node: any) {
        return node.nodeName === 'DIV' &&
          node.classList &&
          node.classList.contains('kg-callout-card');
      },
      replacement: function (content: string, node: any) {
        // Extract emoji and text from the callout card
        const emojiElement = node.querySelector('.kg-callout-emoji');
        const textElement = node.querySelector('.kg-callout-text');

        if (!textElement) return content;

        const emoji = emojiElement ? emojiElement.textContent : '';
        const text = textElement.textContent || '';

        // Map emoji back to callout type
        const calloutType = ContentConverter.getCalloutTypeFromEmoji(emoji);

        return `\n\n> [!${calloutType}]\n> ${text}\n\n`;
      }
    });

    return turndownService.turndown(html);
  }

  static createFilename(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }

  static slugify(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }

  static normalizeFrontMatter(frontMatter: any): any {
    return PropertyMapper.normalizeToGhost(frontMatter);
  }



  static async createGhostPostData(frontMatter: any, markdownContent: string, options: any = {}): Promise<any> {
    const { status = 'draft', isUpdate = false, existingPost = null } = options;

    // Use PropertyMapper to extract and validate Ghost properties from frontmatter
    const ghostProperties = PropertyMapper.ghostPropertiesFromFrontMatter(frontMatter, { isUpdate, existingPost });

    // Override status if provided in options
    if (status !== 'draft') {
      ghostProperties.status = status;
    }

    // Generate excerpt from content
    const normalizedFrontMatter = this.normalizeFrontMatter(frontMatter);
    ghostProperties.custom_excerpt = this.generateExcerpt(normalizedFrontMatter, markdownContent);

    // Handle published_at date carefully
    if (isUpdate && existingPost) {
      // CRITICAL: Include the post ID and updated_at for updates
      ghostProperties.id = existingPost.id;
      ghostProperties.updated_at = existingPost.updated_at;

      // When updating an existing post
      const existingStatus = existingPost.status;
      const existingPublishedAt = existingPost.published_at;

      if (existingStatus === 'draft' && ghostProperties.status === 'published') {
        // Draft → Published transition: Set published_at to now
        ghostProperties.published_at = new Date().toISOString();
      } else if (existingPublishedAt) {
        // Post was already published: Preserve existing published_at date
        ghostProperties.published_at = existingPublishedAt;
      } else if (ghostProperties.status === 'published') {
        // Edge case: Post has no published_at but status is published
        ghostProperties.published_at = new Date().toISOString();
      }
      // For draft posts, don't set published_at (Ghost will handle it)
    } else {
      // Creating new post
      if (ghostProperties.status === 'published') {
        // New published post: Use frontmatter date or current time
        const postDate = this.parseDate(normalizedFrontMatter.published_at) ||
          this.parseDate(normalizedFrontMatter.date) ||
          new Date();
        ghostProperties.published_at = postDate.toISOString();
      }
      // For new draft posts, don't set published_at
    }

    const lexicalResult = await markdownToLexical(markdownContent);

    ghostProperties.lexical = JSON.stringify(lexicalResult.data);
    ghostProperties.mobiledoc = null; // Explicitly set mobiledoc to null for Ghost API

    return ghostProperties;
  }

  static parseDate(dateStr: string): Date | null {
    if (!dateStr) return null;
    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? null : date;
  }

  /**
   * Get callout type from emoji (for reverse conversion)
   */
  static getCalloutTypeFromEmoji(emoji: string): string {
    const emojiToType: Record<string, string> = {
      '📝': 'note',
      'ℹ️': 'info',
      '💡': 'tip',
      '✅': 'success',
      '⚠️': 'warning',
      '❌': 'danger',
      '🚨': 'error',
      '❓': 'question',
      '💬': 'quote',
      '📋': 'example',
      '📄': 'abstract',
      '☑️': 'todo',
      '🐛': 'bug'
    };

    return emojiToType[emoji] || 'note';
  }

  static generateExcerpt(_frontMatter: any, content: string): string | null {
    // Always generate excerpt from content (no frontmatter excerpt support)
    if (content) {
      // Get plain text from markdown
      const plaintext = content.replace(/[#*`_\[\]()]/g, '').trim();

      if (plaintext.length <= 300) {
        return plaintext;
      }

      // Truncate at word boundary
      const truncated = plaintext.substring(0, 297);
      const lastSpace = truncated.lastIndexOf(' ');

      if (lastSpace > 250) {
        return truncated.substring(0, lastSpace) + '...';
      }

      return truncated + '...';
    }

    return null;
  }

  /**
   * Parse article content using Obsidian's metadata cache API
   * @param content - The full file content including frontmatter
   * @param file - TFile for using Obsidian's metadata cache
   * @param app - Obsidian App instance for metadata cache access
   */
  static parseArticle(
    content: string,
    file: any,
    app: any
  ): { frontMatter: any, markdownContent: string } {
    if (!file || !app?.metadataCache) {
      throw new Error('Obsidian file and app instance are required for parsing');
    }

    const cache = app.metadataCache.getFileCache(file);
    if (!cache?.frontmatter) {
      throw new Error('No frontmatter found in file metadata cache');
    }

    // Use Obsidian's getFrontMatterInfo for precise content extraction
    const frontMatterInfo = getFrontMatterInfo(content);
    const markdownContent = frontMatterInfo.exists
      ? content.slice(frontMatterInfo.contentStart).trim()
      : content.trim();

    return {
      frontMatter: cache.frontmatter,
      markdownContent
    };
  }

  static parseMarkdown(
    content: string,
    file: any,
    app: any
  ): { frontMatter: any, markdownContent: string } {
    return this.parseArticle(content, file, app);
  }

  static objectToYaml(obj: any): string {
    let yaml = '';
    for (const [key, value] of Object.entries(obj)) {
      if (Array.isArray(value)) {
        yaml += `${key}:\n`;
        for (const item of value) {
          yaml += `  - ${item}\n`;
        }
      } else if (typeof value === 'string') {
        yaml += `${key}: "${value}"\n`;
      } else if (value === null) {
        // Include null values in frontmatter to show the field exists but has no value
        yaml += `${key}: null\n`;
      } else {
        yaml += `${key}: ${value}\n`;
      }
    }
    return yaml;
  }

  static async convertGhostPostToArticle(post: any): Promise<string> {
    // Check if post has lexical content
    if (!post.lexical) {
      throw new Error(`Post "${post.title}" has no lexical content. Only lexical content is supported.`);
    }

    const tags = post.tags ? post.tags.map((tag: any) => tag.name) : [];
    // Use Ghost's updated_at as the content change time
    const ghostUpdatedTime = post.updated_at;

    console.log('=== CONVERTING GHOST POST TO ARTICLE ===');
    console.log('ghostUpdatedTime (post.updated_at):', ghostUpdatedTime);

    // Create input object for property mapping (WITHOUT internal sync timestamps)
    const inputData = {
      title: post.title,
      slug: post.slug,
      status: post.status || 'draft',
      tags: tags,
      primary_tag: post.primary_tag?.name || null,
      visibility: post.visibility || 'public',
      feature_image: post.feature_image || null,
      featured: post.featured || false,
      newsletter: post.newsletter?.name || null,
      email_sent: post.email ? 'Yes' : 'No',
      created_at: post.created_at,
      updated_at: post.updated_at,
      published_at: post.published_at
    };

    // Create frontmatter using the centralized property mapping
    const frontmatter = PropertyMapper.normalizeToObsidian(inputData);
    const yamlFrontmatter = this.objectToYaml(frontmatter);

    const lexicalDoc = JSON.parse(post.lexical);
    const result = await lexicalToMarkdown(lexicalDoc);
    const fileContent = `---\n${yamlFrontmatter}---\n\n${result.data}`;

    return fileContent;
  }

  static extractMarkdownFromLexical(lexicalDoc: any): string | null {
    // Extract markdown content from lexical document structure
    try {
      if (lexicalDoc?.root?.children) {
        for (const child of lexicalDoc.root.children) {
          // Look for markdown cards
          if (child.type === 'markdown' && child.markdown) {
            return child.markdown;
          }
        }
      }
      return null;
    } catch (error) {
      console.warn('Failed to extract markdown from lexical:', error);
      return null;
    }
  }
}
