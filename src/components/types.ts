import type { GhostP<PERSON>, GhostNewsletter } from '../types';
import type GhostSyncPlugin from '../main';

export type SyncStatus = 'synced' | 'different' | 'unknown' | 'new_post';

export interface SyncStatusData {
  title: SyncStatus;
  slug: SyncStatus;
  status: SyncStatus;
  tags: SyncStatus;
  featured: SyncStatus;
  feature_image: SyncStatus;
  visibility: SyncStatus;
  primary_tag: SyncStatus;
  created_at: SyncStatus;
  updated_at: SyncStatus;
  published_at: SyncStatus;
  synced_at: SyncStatus;
  newsletter: SyncStatus;
  email_sent: SyncStatus;
  ghostPost?: GhostPost;
  // Internal sync metadata for display
  localSyncedAt?: string;
  localChangedAt?: string;
  // New post specific properties
  isNewPost?: boolean;
  localTitle?: string;
  localSlug?: string;
  // Local frontmatter data for comparison display
  localTags?: string[];
}

export interface PublishOptions {
  newsletter?: GhostNewsletter;
  emailSegment?: string;
  testMode?: boolean;
  action: 'publish' | 'publish-send' | 'send-only';
  scheduleType?: 'now' | 'scheduled';
  scheduledDate?: string; // ISO string for scheduled publication
}

export interface PluginContext {
  plugin: GhostSyncPlugin;
}
