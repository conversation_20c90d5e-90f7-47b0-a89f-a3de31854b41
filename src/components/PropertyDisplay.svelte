<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import type { SyncStatus } from './types';

  export let label: string;
  export let status: SyncStatus;
  export let value: any;
  export let isTagsList: boolean = false;
  export let primaryTag: string | null = null;
  export let deletedTags: string[] = [];

  const dispatch = createEventDispatcher();

  $: displayValue = formatValue(value, isTagsList);

  function formatValue(val: any, isTags: boolean): string {
    if (val === null || val === undefined) {
      return 'None';
    }

    if (typeof val === 'boolean') {
      return val ? 'Yes' : 'No';
    }

    if (isTags && Array.isArray(val)) {
      if (val.length === 0) {
        return 'None';
      }
      return val
        .filter(tag => tag !== null && tag !== undefined)
        .map(tag => tag.name || tag)
        .filter(name => name !== null && name !== undefined && name !== '')
        .join(', ') || 'None';
    }

    if (typeof val === 'object' && val.name) {
      return val.name;
    }

    return String(val);
  }

  function getTagsArray(val: any): Array<{name: string, isPrimary: boolean, isDeleted: boolean}> {
    if (!val || !Array.isArray(val)) {
      return [];
    }
    return val
      .filter(tag => tag !== null && tag !== undefined)
      .map(tag => {
        const tagName = tag.name || tag;
        return {
          name: tagName,
          isPrimary: primaryTag && tagName === primaryTag,
          isDeleted: deletedTags.includes(tagName)
        };
      })
      .filter(tag => tag.name && typeof tag.name === 'string' && tag.name.trim() !== '');
  }

  function handleTagDelete(tagName: string) {
    dispatch('tag-delete', { tagName });
  }
</script>

<div class="ghost-property-section">
  <div class="ghost-property-header">
    <span class="ghost-property-label">
      {label}
      {#if status === 'different'}
        <span class="ghost-property-refresh-icon" title="Out of sync">🔄</span>
      {/if}
    </span>
  </div>

  {#if isTagsList && value && Array.isArray(value) && value.length > 0}
    <div class="ghost-tags-container">
      {#each getTagsArray(value) as tag}
        <span class="ghost-tag-pill" class:primary={tag.isPrimary} class:deleted={tag.isDeleted}>
          <span class="ghost-tag-name">{tag.name}</span>
          <button
            class="ghost-tag-delete"
            on:click={() => handleTagDelete(tag.name)}
            title="Remove tag"
          >
            ×
          </button>
        </span>
      {/each}
    </div>
  {:else}
    <div class="ghost-property-field">
      {displayValue}
    </div>
  {/if}
</div>

<style>
  .ghost-property-section {
    margin-bottom: 20px;
  }

  .ghost-property-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  .ghost-property-label {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-normal);
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .ghost-property-refresh-icon {
    font-size: 12px;
    opacity: 0.6;
    color: var(--text-muted);
  }

  .ghost-property-field {
    background: var(--background-secondary);
    border: 1px solid var(--background-modifier-border);
    border-radius: 6px;
    padding: 12px 16px;
    font-size: 14px;
    color: var(--text-normal);
    min-height: 20px;
    display: flex;
    align-items: center;
  }

  .ghost-tags-container {
    background: var(--background-secondary);
    border: 1px solid var(--background-modifier-border);
    border-radius: 6px;
    padding: 8px 12px;
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    min-height: 36px;
    align-items: center;
  }

  .ghost-tag-pill {
    background: var(--background-modifier-border);
    color: var(--text-normal);
    padding: 4px 8px 4px 10px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    line-height: 1.2;
    display: flex;
    align-items: center;
    gap: 4px;
    position: relative;
    transition: all 0.2s ease;
  }

  .ghost-tag-pill:hover {
    background: var(--background-modifier-hover);
  }

  .ghost-tag-pill.primary {
    background: var(--interactive-accent);
    color: var(--text-on-accent);
  }

  .ghost-tag-pill.primary:hover {
    background: var(--interactive-accent-hover);
  }

  .ghost-tag-pill.deleted {
    opacity: 0.6;
    text-decoration: line-through;
    background: var(--color-red);
    color: white;
  }

  .ghost-tag-name {
    flex: 1;
  }

  .ghost-tag-delete {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    padding: 0;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    opacity: 0.7;
    transition: all 0.2s ease;
  }

  .ghost-tag-delete:hover {
    opacity: 1;
    background: rgba(255, 255, 255, 0.2);
  }

  .ghost-tag-pill.deleted .ghost-tag-delete {
    display: none;
  }
</style>
