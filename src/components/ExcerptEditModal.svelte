<script lang="ts">
  import { createEventDispatcher } from 'svelte';

  export let show: boolean = false;
  export let excerpt: string = '';
  export let postTitle: string = '';

  const dispatch = createEventDispatcher<{
    save: { excerpt: string };
    cancel: void;
  }>();

  let editedExcerpt = excerpt;
  let textareaElement: HTMLTextAreaElement;

  $: if (show && textareaElement) {
    // Focus and select all text when modal opens
    setTimeout(() => {
      textareaElement.focus();
      textareaElement.select();
    }, 100);
  }

  $: if (show) {
    editedExcerpt = excerpt;
  }

  function handleSave() {
    dispatch('save', { excerpt: editedExcerpt });
  }

  function handleCancel() {
    editedExcerpt = excerpt; // Reset to original
    dispatch('cancel');
  }

  function handleBackdropClick(event: MouseEvent) {
    if (event.target === event.currentTarget) {
      handleCancel();
    }
  }

  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      handleCancel();
    } else if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
      handleSave();
    }
  }


</script>

{#if show}
  <div
    class="modal-backdrop"
    data-modal-type="excerpt-edit"
    role="button"
    tabindex="0"
    on:click={handleBackdropClick}
    on:keydown={handleKeydown}
  >
    <div class="modal-content excerpt-edit-modal">
      <div class="excerpt-edit-header">
        <h2>Edit Excerpt</h2>
        <button class="close-btn" on:click={handleCancel}>×</button>
      </div>

      <div class="excerpt-edit-content">
        <div class="excerpt-edit-field">
          <textarea
            id="excerpt-textarea"
            data-input="excerpt"
            bind:this={textareaElement}
            bind:value={editedExcerpt}
            placeholder="Enter excerpt for {postTitle}..."
            rows="8"
            maxlength="300"
          ></textarea>
          <div class="excerpt-char-count">
            {editedExcerpt.length}/300 characters
          </div>
        </div>
      </div>

      <div class="excerpt-edit-buttons">
        <button
          class="ghost-sync-btn"
          data-action="cancel"
          on:click={handleCancel}
        >
          Cancel
        </button>
        <button
          class="ghost-sync-btn mod-cta"
          data-action="save"
          on:click={handleSave}
        >
          Save
        </button>
      </div>
    </div>
  </div>
{/if}

<style>
  .modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .modal-content {
    background: var(--background-primary);
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--background-modifier-border);
  }

  .excerpt-edit-modal {
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
  }

  .excerpt-edit-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 20px 0 20px;
    border-bottom: 1px solid var(--background-modifier-border);
    margin-bottom: 20px;
  }

  .excerpt-edit-header h2 {
    margin: 0;
    color: var(--text-normal);
    font-size: 18px;
    font-weight: 600;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  .close-btn:hover {
    color: var(--text-normal);
    background: var(--background-modifier-hover);
  }

  .excerpt-edit-content {
    padding: 0 20px;
  }

  .excerpt-edit-field {
    margin-bottom: 20px;
  }

  .excerpt-edit-field textarea {
    width: 100%;
    height: 160px;
    padding: 12px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 6px;
    background: var(--background-primary);
    color: var(--text-normal);
    font-family: var(--font-interface);
    font-size: 14px;
    line-height: 1.4;
    resize: none;
    overflow-y: auto;
    box-sizing: border-box;
  }

  .excerpt-edit-field textarea:focus {
    outline: none;
    border-color: var(--interactive-accent);
    box-shadow: 0 0 0 2px rgba(var(--interactive-accent-rgb), 0.2);
  }

  .excerpt-char-count {
    margin-top: 8px;
    font-size: 12px;
    color: var(--text-muted);
    text-align: right;
  }

  .excerpt-edit-buttons {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    padding: 20px;
    border-top: 1px solid var(--background-modifier-border);
  }

  .excerpt-edit-buttons .ghost-sync-btn {
    min-width: 100px;
  }
</style>
