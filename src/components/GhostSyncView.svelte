<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { TFile } from 'obsidian';
  import PropertyDisplay from './PropertyDisplay.svelte';
  import StatusBadges from './StatusBadges.svelte';
  import DeleteConfirmDialog from './DeleteConfirmDialog.svelte';
  import type { SyncStatusData } from './types';
  import * as path from 'path';

  export let currentFile: TFile | null = null;
  export let syncStatus: SyncStatusData = {
    title: 'unknown',
    slug: 'unknown',
    status: 'unknown',
    tags: 'unknown',
    featured: 'unknown',
    feature_image: 'unknown',
    visibility: 'unknown',
    primary_tag: 'unknown',
    created_at: 'unknown',
    updated_at: 'unknown',
    published_at: 'unknown',
    synced_at: 'unknown',
    newsletter: 'unknown',
    email_sent: 'unknown'
  };

  // Helper function to format timestamps according to locale
  function formatTimestamp(timestamp: string | null | undefined): string {
    if (!timestamp || timestamp === 'Never' || timestamp === 'Unknown') {
      return timestamp || 'Never';
    }

    try {
      const date = new Date(timestamp);
      if (isNaN(date.getTime())) {
        return timestamp;
      }

      // Format according to user's locale with relative time when recent
      const now = new Date();
      const diffMs = now.getTime() - date.getTime();
      const diffMinutes = Math.floor(diffMs / (1000 * 60));
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

      // Show relative time for recent timestamps
      if (diffMinutes < 1) {
        return 'Just now';
      } else if (diffMinutes < 60) {
        return `${diffMinutes}m ago`;
      } else if (diffHours < 24) {
        return `${diffHours}h ago`;
      } else if (diffDays < 7) {
        return `${diffDays}d ago`;
      } else {
        // For older timestamps, show formatted date
        return date.toLocaleDateString(undefined, {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        });
      }
    } catch (error) {
      return timestamp;
    }
  }
  export let plugin: any = null;

  const dispatch = createEventDispatcher();

  let isInArticlesDir = false;
  let articlesPath = '';
  let showDeleteDialog = false;
  let deletedTags: string[] = [];
  let excerptExpanded = false;

  $: {
    if (currentFile && plugin?.settings?.articlesDir) {
      articlesPath = path.normalize(plugin.settings.articlesDir);
      const filePath = path.normalize(currentFile.path);
      isInArticlesDir = filePath.startsWith(articlesPath);
    } else {
      isInArticlesDir = false;
    }
  }

  async function handleSmartSync() {
    dispatch('smartSync');
  }

  function handlePublish() {
    dispatch('publish');
  }

  function handleBrowsePosts() {
    if (plugin && plugin.browseGhostPosts) {
      plugin.browseGhostPosts();
    } else {
      dispatch('browsePosts');
    }
  }

  function handleRefresh() {
    dispatch('refresh');
  }

  function handleDelete() {
    showDeleteDialog = true;
  }

  function handleDeleteConfirm(event: CustomEvent) {
    const { deleteOption } = event.detail;
    dispatch('delete', { deleteOption });
    showDeleteDialog = false;
  }

  function handleDeleteCancel() {
    showDeleteDialog = false;
  }

  function handleTagDelete(event: CustomEvent) {
    const { tagName } = event.detail;
    if (!deletedTags.includes(tagName)) {
      deletedTags = [...deletedTags, tagName];
      dispatch('tag-delete', { tagName, deletedTags });
    }
  }

  function handleExcerptClick() {
    dispatch('excerptEdit', {
      excerpt: syncStatus.ghostPost?.excerpt || '',
      postTitle: syncStatus.ghostPost?.title || 'Untitled'
    });
  }

  function toggleExcerptExpanded() {
    excerptExpanded = !excerptExpanded;
  }

  function getDisplayExcerpt(excerpt: string | undefined): string {
    if (!excerpt) return 'No excerpt available';
    if (excerptExpanded || excerpt.length <= 300) return excerpt;
    return excerpt.substring(0, 300) + '...';
  }
</script>

<div class="ghost-sync-status-view">
  {#if !currentFile}
    <p class="ghost-sync-no-file">No file selected</p>
  {:else if !isInArticlesDir}
    <p class="ghost-sync-not-article">
      File must be in {plugin.settings.articlesDir} directory
    </p>
  {:else if syncStatus.isNewPost}
    <!-- New Post Status -->
    <div class="ghost-sync-new-post">
      <div class="ghost-sync-new-post-icon">📝</div>
      <div class="ghost-sync-new-post-content">
        <h3>New Post</h3>
        <p>This post hasn't been created in Ghost yet.</p>
        <div class="ghost-sync-new-post-details">
          <div><strong>Title:</strong> {syncStatus.localTitle || 'Untitled'}</div>
          <div><strong>Slug:</strong> {syncStatus.localSlug || 'No slug'}</div>
        </div>
        <p class="ghost-sync-new-post-hint">Click "Sync" to create it in Ghost.</p>
      </div>
    </div>

    <!-- Buttons for New Post -->
    <div class="ghost-sync-buttons">
      <button
        class="ghost-sync-btn mod-cta"
        on:click={handleSmartSync}
      >
        Sync to Ghost
      </button>

      <button
        class="ghost-sync-btn"
        on:click={handleBrowsePosts}
      >
        Browse Posts
      </button>
    </div>
  {:else}

    <!-- Feature Image Preview -->
    {#if syncStatus.ghostPost?.feature_image}
      <div class="ghost-sync-feature-image-container">
        <img
          src={syncStatus.ghostPost.feature_image}
          alt="Featured content"
          class="ghost-sync-feature-image"
        />
      </div>
    {/if}

    <!-- Status Badges -->
    <StatusBadges
      status={syncStatus.ghostPost?.status}
      visibility={syncStatus.ghostPost?.visibility}
      featured={syncStatus.ghostPost?.featured}
    />

    <!-- Properties Section -->
    <div class="ghost-properties">
      <PropertyDisplay
        label="Title"
        status={syncStatus.title}
        value={syncStatus.ghostPost?.title}
      />
      <PropertyDisplay
        label="Slug"
        status={syncStatus.slug}
        value={syncStatus.ghostPost?.slug}
      />
      <PropertyDisplay
        label="Tags"
        status={syncStatus.tags}
        value={syncStatus.tags === 'different' && syncStatus.localTags ? syncStatus.localTags : syncStatus.ghostPost?.tags}
        isTagsList={true}
        primaryTag={syncStatus.ghostPost?.primary_tag?.name}
        deletedTags={deletedTags}
        on:tag-delete={handleTagDelete}
      />
    </div>

    <!-- Excerpt Section -->
    {#if syncStatus.ghostPost?.excerpt !== undefined}
      <div class="ghost-excerpt-section">
        <div class="ghost-excerpt-header">
          <span class="ghost-excerpt-label">Excerpt</span>
          <button
            class="ghost-excerpt-edit-btn"
            on:click={handleExcerptClick}
            title="Edit excerpt"
          >
            ✏️
          </button>
        </div>
        <div class="ghost-excerpt-content">
          <div
            class="ghost-excerpt-text"
            on:click={handleExcerptClick}
            on:keydown={(e) => e.key === 'Enter' && handleExcerptClick()}
            tabindex="0"
            role="button"
          >
            {getDisplayExcerpt(syncStatus.ghostPost?.excerpt)}
          </div>
          {#if syncStatus.ghostPost?.excerpt && syncStatus.ghostPost.excerpt.length > 300}
            <button
              class="ghost-excerpt-expand-btn"
              on:click={toggleExcerptExpanded}
            >
              {excerptExpanded ? 'Show less' : 'Show more'}
            </button>
          {/if}
        </div>
      </div>
    {/if}

    <!-- Newsletter Section -->
    {#if syncStatus.ghostPost?.newsletter || syncStatus.ghostPost?.email}
      <div class="ghost-newsletter-section">
        <div class="ghost-newsletter-header">
          <span class="ghost-newsletter-label">
            {syncStatus.ghostPost?.newsletter?.name || 'Newsletter'}
            {#if syncStatus.newsletter === 'different'}
              <span class="ghost-property-refresh-icon" title="Out of sync">🔄</span>
            {/if}
          </span>
          {#if syncStatus.ghostPost?.email}
            <span class="ghost-newsletter-status-badge">Sent</span>
          {:else}
            <span class="ghost-newsletter-status-badge ghost-newsletter-status-optional">Optional</span>
          {/if}
        </div>
      </div>
    {/if}

    <!-- Buttons -->
    <div class="ghost-sync-buttons">
      {#if syncStatus.ghostPost}
        <button
          class="ghost-sync-btn mod-cta"
          class:disabled={syncStatus.ghostPost.status === 'published' && !!syncStatus.ghostPost.email}
          disabled={syncStatus.ghostPost.status === 'published' && !!syncStatus.ghostPost.email}
          on:click={handlePublish}
        >
          Publish
        </button>
      {/if}

      <!-- Sync and Refresh Row -->
      <div class="ghost-sync-row">
        <button
          class="ghost-sync-btn ghost-sync-btn-half"
          on:click={handleSmartSync}
        >
          Sync
        </button>

        {#if currentFile && !syncStatus.isNewPost}
          <button
            class="ghost-sync-btn ghost-sync-btn-half"
            on:click={handleRefresh}
            title="Refresh sync status from Ghost"
          >
            Refresh
          </button>
        {/if}
      </div>

      <button
        class="ghost-sync-btn"
        on:click={handleBrowsePosts}
      >
        Browse Posts
      </button>

      {#if syncStatus.ghostPost}
        <button
          class="ghost-sync-btn ghost-delete-btn"
          on:click={handleDelete}
        >
          🗑️ Delete
        </button>
      {/if}
    </div>

    <!-- Sync Metadata Section - Moved to bottom and made subtle -->
    <div class="ghost-sync-metadata-subtle">
      <div class="ghost-sync-metadata-item">
        <span class="label">Last Synced:</span>
        <span class="value">{formatTimestamp(syncStatus.localSyncedAt)}</span>
      </div>
      <div class="ghost-sync-metadata-item">
        <span class="label">Last Changed:</span>
        <span class="value">{formatTimestamp(syncStatus.localChangedAt)}</span>
      </div>
    </div>

  {/if}
</div>

<!-- Delete Confirmation Dialog -->
<DeleteConfirmDialog
  bind:show={showDeleteDialog}
  postTitle={syncStatus.ghostPost?.title || 'Untitled'}
  on:confirm={handleDeleteConfirm}
  on:cancel={handleDeleteCancel}
/>



<style>
  .ghost-sync-feature-image-container {
    margin-bottom: 16px;
  }

  .ghost-sync-feature-image {
    width: 100%;
    max-height: 200px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid var(--background-modifier-border);
  }

  .ghost-sync-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .ghost-sync-btn.disabled:hover {
    opacity: 0.5;
  }

  .ghost-sync-metadata {
    margin-top: 16px;
    padding: 12px;
    background: var(--background-secondary);
    border-radius: 6px;
    border: 1px solid var(--background-modifier-border);
  }

  .ghost-sync-metadata h4 {
    margin: 0 0 8px 0;
    font-size: 12px;
    font-weight: 600;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .ghost-sync-properties-section {
    margin-top: 16px;
    padding: 12px;
    background: var(--background-secondary);
    border-radius: 6px;
    border: 1px solid var(--background-modifier-border);
  }

  .ghost-sync-properties-section h4 {
    margin: 0 0 8px 0;
    font-size: 12px;
    font-weight: 600;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  /* Newsletter section */
  .ghost-newsletter-section {
    margin-bottom: 20px;
    padding: 12px;
    background: var(--background-secondary);
    border-radius: 6px;
    border: 1px solid var(--background-modifier-border);
  }

  .ghost-newsletter-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .ghost-newsletter-label {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-normal);
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .ghost-newsletter-status-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    background: var(--color-green);
    color: white;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .ghost-newsletter-status-badge.ghost-newsletter-status-optional {
    background: var(--text-muted);
    opacity: 0.7;
  }

  .ghost-sync-metadata-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 0;
    font-size: 12px;
  }

  .ghost-sync-metadata-item .label {
    color: var(--text-muted);
    font-weight: 500;
  }

  .ghost-sync-metadata-item .value {
    color: var(--text-normal);
    font-family: var(--font-monospace);
  }

  /* Subtle metadata section at bottom */
  .ghost-sync-metadata-subtle {
    margin-top: 20px;
    padding: 8px 12px;
    background: transparent;
    border-top: 1px solid var(--background-modifier-border-hover);
    opacity: 0.7;
  }

  .ghost-sync-metadata-subtle .ghost-sync-metadata-item {
    padding: 2px 0;
    font-size: 11px;
  }

  .ghost-sync-metadata-subtle .label {
    color: var(--text-faint);
    font-weight: normal;
  }

  .ghost-sync-metadata-subtle .value {
    color: var(--text-muted);
    font-family: var(--font-interface);
    font-size: 11px;
  }

  /* Refresh button in header */
  .ghost-sync-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }



  /* Properties section */
  .ghost-properties {
    margin-bottom: 20px;
  }

  /* Excerpt section */
  .ghost-excerpt-section {
    margin-bottom: 20px;
    padding: 12px;
    background: var(--background-secondary);
    border-radius: 6px;
    border: 1px solid var(--background-modifier-border);
  }

  .ghost-excerpt-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
  }

  .ghost-excerpt-label {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-normal);
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .ghost-excerpt-edit-btn {
    background: none;
    border: none;
    padding: 2px 4px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    color: var(--text-muted);
    transition: all 0.2s ease;
  }

  .ghost-excerpt-edit-btn:hover {
    background: var(--background-modifier-hover);
    color: var(--text-normal);
  }

  .ghost-excerpt-content {
    position: relative;
  }

  .ghost-excerpt-text {
    margin: 0;
    font-size: 13px;
    line-height: 1.4;
    color: var(--text-muted);
    cursor: pointer;
    transition: color 0.2s ease;
  }

  .ghost-excerpt-text:hover {
    color: var(--text-normal);
  }

  .ghost-excerpt-expand-btn {
    background: none;
    border: none;
    padding: 4px 0;
    margin-top: 4px;
    font-size: 11px;
    color: var(--interactive-accent);
    cursor: pointer;
    text-decoration: underline;
    transition: opacity 0.2s ease;
  }

  .ghost-excerpt-expand-btn:hover {
    opacity: 0.8;
  }
</style>
