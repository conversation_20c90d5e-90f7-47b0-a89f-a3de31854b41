<script lang="ts">
  import { createEventDispatcher } from 'svelte';

  export let show: boolean = false;
  export let postTitle: string = '';

  const dispatch = createEventDispatcher();

  let selectedOption: 'both' | 'ghost-only' = 'both';

  function handleConfirm() {
    dispatch('confirm', { deleteOption: selectedOption });
    show = false;
  }

  function handleCancel() {
    dispatch('cancel');
    show = false;
  }

  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      handleCancel();
    } else if (event.key === 'Enter') {
      handleConfirm();
    }
  }
</script>

{#if show}
  <!-- svelte-ignore a11y-click-events-have-key-events -->
  <!-- svelte-ignore a11y-no-static-element-interactions -->
  <div class="delete-confirm-overlay" on:click={handleCancel} on:keydown={handleKeydown}>
    <!-- svelte-ignore a11y-click-events-have-key-events -->
    <!-- svelte-ignore a11y-no-static-element-interactions -->
    <div class="delete-confirm-modal" on:click|stopPropagation>
      <div class="delete-confirm-header">
        <h2>Delete Post</h2>
        <div class="delete-confirm-warning">⚠️</div>
      </div>

      <div class="delete-confirm-content">
        <p class="delete-confirm-message">
          Are you sure you want to delete "<strong>{postTitle}</strong>"?
        </p>

        <div class="delete-confirm-options">
          <label class="delete-confirm-option">
            <input
              type="radio"
              bind:group={selectedOption}
              value="both"
              class="delete-confirm-radio"
            />
            <div class="delete-confirm-option-content">
              <div class="delete-confirm-option-title">Delete from both Ghost and Obsidian</div>
              <div class="delete-confirm-option-desc">
                This will permanently delete the post from Ghost and remove the file from Obsidian
              </div>
            </div>
          </label>

          <label class="delete-confirm-option">
            <input
              type="radio"
              bind:group={selectedOption}
              value="ghost-only"
              class="delete-confirm-radio"
            />
            <div class="delete-confirm-option-content">
              <div class="delete-confirm-option-title">Delete from Ghost only</div>
              <div class="delete-confirm-option-desc">
                This will delete the post from Ghost but keep the file in Obsidian
              </div>
            </div>
          </label>
        </div>
      </div>

      <div class="delete-confirm-buttons">
        <button
          class="delete-confirm-btn delete-confirm-btn-cancel"
          on:click={handleCancel}
        >
          Cancel
        </button>
        <button
          class="delete-confirm-btn delete-confirm-btn-delete"
          on:click={handleConfirm}
        >
          Delete
        </button>
      </div>
    </div>
  </div>
{/if}

<style>
  .delete-confirm-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .delete-confirm-modal {
    background: var(--background-primary);
    border-radius: 8px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--background-modifier-border);
  }

  .delete-confirm-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 20px 0 20px;
    border-bottom: 1px solid var(--background-modifier-border);
    margin-bottom: 20px;
  }

  .delete-confirm-header h2 {
    margin: 0;
    color: var(--text-normal);
    font-size: 18px;
    font-weight: 600;
  }

  .delete-confirm-warning {
    font-size: 24px;
    color: var(--color-red);
  }

  .delete-confirm-content {
    padding: 0 20px;
  }

  .delete-confirm-message {
    margin-bottom: 20px;
    color: var(--text-normal);
    font-size: 14px;
    line-height: 1.5;
  }

  .delete-confirm-options {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
  }

  .delete-confirm-option {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .delete-confirm-option:hover {
    background: var(--background-modifier-hover);
    border-color: var(--background-modifier-border-hover);
  }

  .delete-confirm-option:has(input:checked) {
    background: var(--background-modifier-active);
    border-color: var(--interactive-accent);
  }

  .delete-confirm-radio {
    margin-top: 2px;
    accent-color: var(--interactive-accent);
  }

  .delete-confirm-option-content {
    flex: 1;
  }

  .delete-confirm-option-title {
    font-weight: 500;
    color: var(--text-normal);
    margin-bottom: 4px;
  }

  .delete-confirm-option-desc {
    font-size: 12px;
    color: var(--text-muted);
    line-height: 1.4;
  }

  .delete-confirm-buttons {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    padding: 20px;
    border-top: 1px solid var(--background-modifier-border);
  }

  .delete-confirm-btn {
    padding: 8px 16px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .delete-confirm-btn-cancel {
    background: var(--background-secondary);
    color: var(--text-normal);
  }

  .delete-confirm-btn-cancel:hover {
    background: var(--background-modifier-hover);
  }

  .delete-confirm-btn-delete {
    background: var(--color-red);
    color: white;
    border-color: var(--color-red);
  }

  .delete-confirm-btn-delete:hover {
    background: var(--color-red);
    opacity: 0.8;
  }
</style>
